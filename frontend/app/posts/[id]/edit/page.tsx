"use client";

import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import PostForm from "@/components/PostForm";
import { api } from "@/lib/axios";

export default function EditPostPage() {
  const params = useParams();
  const { user } = useAuth();
  const router = useRouter();
  const id = params.id as string;

  const { data: post, isLoading } = useQuery({
    queryKey: ['post', id],
    queryFn: () => api.get(`/posts/${id}`).then(res => res.data),
  });

  if (!user) {
    router.push("/login");
    return null;
  }

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (user.id !== post.author_id) {
    router.push(`/posts/${id}`);
    return null;
  }

  const onSubmit = async (values: { title: string; content: string }) => {
    try {
      await api.patch(`/posts/${id}/`, values);
      router.push(`/posts/${id}`);
    } catch (error) {
      console.error("Failed to update post:", error);
    }
  };

  return (
    <div className="max-w-3xl mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Edit Post</h1>
      <PostForm 
        onSubmit={onSubmit} 
        initialValues={{ title: post.title, content: post.content }} 
        isSubmitting={false} 
      />
    </div>
  );
}