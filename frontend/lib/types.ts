export interface User {
    id: number;
    username: string;
    email: string;
  }
  
  export interface Post {
    id: number;
    title: string;
    content: string;
    author: string;
    author_id: number;
    created_at: string;
    updated_at: string;
    comments: Comment[];
  }
  
  export interface Comment {
    id: number;
    content: string;
    author: string;
    author_id: number;
    created_at: string;
    post: number;
  }