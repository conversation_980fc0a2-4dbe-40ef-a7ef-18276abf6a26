import { api } from './axios';
import { Post } from './types';

export async function getPosts(): Promise<{ results: Post[] }> {
  try {
    const response = await api.get('/posts/');
    console.log('API Response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in getPosts:', error);
    throw error;
  }
}

export async function getPost(id: number): Promise<Post> {
  try {
    const response = await api.get(`/posts/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error in getPost(${id}):`, error);
    throw error;
  }
}

export async function createPost(data: Partial<Post>): Promise<Post> {
  try {
    const response = await api.post('/posts/', data);
    return response.data;
  } catch (error) {
    console.error('Error in createPost:', error);
    throw error;
  }
}

export async function updatePost(id: number, data: Partial<Post>): Promise<Post> {
  try {
    const response = await api.patch(`/posts/${id}/`, data);
    return response.data;
  } catch (error) {
    console.error(`Error in updatePost(${id}):`, error);
    throw error;
  }
}

export async function deletePost(id: number): Promise<void> {
  try {
    await api.delete(`/posts/${id}/`);
  } catch (error) {
    console.error(`Error in deletePost(${id}):`, error);
    throw error;
  }
} 