"use client";

import CommentForm from "@/components/CommentForm";
import CommentItem from "@/components/CommentItem";
import { Skeleton } from "@/components/ui/skeleton";
import { Comment } from "@/lib/types";

interface CommentSectionProps {
  postId: number;
  comments: Comment[];
}

export default function CommentSection({ postId, comments }: CommentSectionProps) {
  return (
    <div className="mt-12">
      <h2 className="text-xl font-bold mb-4">Comments</h2>
      <CommentForm postId={postId} />
      <div className="mt-6 space-y-6">
        {comments?.length > 0 ? (
          comments.map((comment: Comment) => (
            <CommentItem key={comment.id} comment={comment} />
          ))
        ) : (
          <p className="text-muted-foreground">No comments yet.</p>
        )}
      </div>
    </div>
  );
}