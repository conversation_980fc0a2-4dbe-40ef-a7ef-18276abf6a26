"use client"

import Link from "next/link";
import { Post } from "@/lib/types";
import { formatDate } from "@/lib/utils";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";

interface PostCardProps {
  post: Post;
}

export default function PostCard({ post }: PostCardProps) {
  if (!post) return null;

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle>
          <Link href={`/posts/${post.id}`} className="hover:underline">
            {post.title}
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-grow">
        <p className="text-gray-500 dark:text-gray-400 line-clamp-3">
          {post.content}
        </p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <span className="text-sm text-muted-foreground">By {post.author}</span>
        <span className="text-sm text-muted-foreground">{formatDate(post.created_at)}</span>
      </CardFooter>
    </Card>
  );
}