import { Comment } from "@/lib/types";
import { formatDate } from "@/lib/utils";

interface CommentItemProps {
  comment: Comment;
}

export default function CommentItem({ comment }: CommentItemProps) {
  return (
    <div className="border rounded-lg p-4">
      <div className="flex justify-between items-center mb-2">
        <span className="font-medium">{comment.author}</span>
        <span className="text-sm text-muted-foreground">{formatDate(comment.created_at)}</span>
      </div>
      <p className="text-muted-foreground">{comment.content}</p>
    </div>
  );
}