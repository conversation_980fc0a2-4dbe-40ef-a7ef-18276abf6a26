"use client"

import { useEffect, useState } from 'react'
import { Post } from '@/lib/types'
import { getPost } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { formatDate } from '@/lib/utils'
import Link from "next/link";
import { useAuth } from "@/hooks/use-auth";
import CommentSection from "@/components/CommentSection";

interface PostDetailProps {
  postId: string
}

export default function PostDetail({ postId }: PostDetailProps) {
  const { user } = useAuth();
  const [post, setPost] = useState<Post | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchPost = async () => {
    setLoading(true)
    setError(null)
    try {
      const data = await getPost(parseInt(postId))
      console.log('Fetched post:', data)
      setPost(data)
    } catch (err) {
      console.error('Error fetching post:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch post')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPost()
  }, [postId])

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-3/4" />
        <Skeleton className="h-4 w-1/4" />
        <Skeleton className="h-32 w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
        <Button onClick={fetchPost} >
          Try Again
        </Button>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="text-center py-8 text-gray-500">
        Post not found
      </div>
    )
  }

  return (
    <article className="prose dark:prose-invert max-w-none">
      <h1>{post.title}</h1>
      <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
        <span>By {post.author}</span>
        <span>{formatDate(post.created_at)}</span>
        {user?.id === post.author_id && (
          <Link href={`/posts/${post.id}/edit`}>
            <Button >Edit</Button>
          </Link>
        )}
      </div>
      <div className="mt-6">
        {post.content}
      </div>
      <div className="mt-8">
        <CommentSection postId={parseInt(postId)} comments={post.comments} />
      </div>
    </article>
  )
}