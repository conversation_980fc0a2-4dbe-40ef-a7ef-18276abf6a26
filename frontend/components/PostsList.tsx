"use client"

import { useEffect, useState } from 'react'
import PostCard from '@/components/PostCard'
import { Skeleton } from '@/components/ui/skeleton'
import { Post } from '@/lib/types'
import { getPosts } from '@/lib/api'
import { Button } from '@/components/ui/button'

export default function PostsList() {
  const [posts, setPosts] = useState<{ results: Post[] } | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchPosts = async () => {
    setLoading(true)
    setError(null)
    try {
      const data = await getPosts()
      console.log('Fetched posts:', data)
      setPosts(data)
    } catch (err) {
      console.error('Error fetching posts:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch posts')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPosts()
  }, [])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-48 rounded-xl" />
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
        <Button onClick={fetchPosts} >
          Try Again
        </Button>
      </div>
    )
  }

  if (!posts?.results?.length) {
    return (
      <div className="text-center py-8 text-gray-500">
        No posts found
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Latest Posts</h1>
          <p className="text-muted-foreground">
            Discover the latest articles and insights
          </p>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.results.map((post: Post) => (
          <PostCard key={post.id} post={post} />
        ))}
      </div>
    </div>
  )
} 