"use client"

import * as React from "react"
import {
  BookOpen,
  Edit3,
  Home,
  PenTool,
  Settings2,
  User,
  Users,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

// Blog application data
const data = {
  user: {
    name: "Blog User",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
  teams: [
    {
      name: "Fullstax Blog",
      logo: BookO<PERSON>,
      plan: "Personal",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/",
      icon: Home,
      isActive: true,
      items: [
        {
          title: "Overview",
          url: "/",
        },
        {
          title: "Analytics",
          url: "/analytics",
        },
      ],
    },
    {
      title: "Posts",
      url: "/posts",
      icon: BookOpen,
      items: [
        {
          title: "All Posts",
          url: "/posts",
        },
        {
          title: "Create Post",
          url: "/posts/create",
        },
        {
          title: "Drafts",
          url: "/posts/drafts",
        },
      ],
    },
    {
      title: "Content",
      url: "#",
      icon: Edit3,
      items: [
        {
          title: "Categories",
          url: "/categories",
        },
        {
          title: "Tags",
          url: "/tags",
        },
        {
          title: "Comments",
          url: "/comments",
        },
      ],
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "Profile",
          url: "/profile",
        },
        {
          title: "Account",
          url: "/account",
        },
        {
          title: "Preferences",
          url: "/preferences",
        },
      ],
    },
  ],
  projects: [
    {
      name: "Recent Posts",
      url: "/posts/recent",
      icon: PenTool,
    },
    {
      name: "User Management",
      url: "/users",
      icon: Users,
    },
    {
      name: "Profile",
      url: "/profile",
      icon: User,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
